const { DataTypes } = require('sequelize');
const AppModel = require('./AppModel');

class UserJobVacancy extends AppModel {
  static schema() {
    return {
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
      },
      job_vacancy_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'job_vacancies',
          key: 'id',
        },
      },
      competency_match: {
        type: DataTypes.FLOAT,
        allowNull: false,
        defaultValue: 0,
        validate: {
          min: 0,
          max: 1,
        },
      },
      skill_match: {
        type: DataTypes.FLOAT,
        allowNull: false,
        defaultValue: 0,
        validate: {
          min: 0,
          max: 1,
        },
      },
      status: {
        type: DataTypes.ENUM('recommended', 'not_recommended'),
        allowNull: false,
        defaultValue: 'not_recommended',
      },
    };
  }

  static associate(models) {
    // A UserJobVacancy record belongs to one User
    this.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user',
    });

    // A UserJobVacancy record belongs to one JobVacancy
    this.belongsTo(models.JobVacancy, {
      foreignKey: 'job_vacancy_id',
      as: 'jobVacancy',
    });
  }

  static options() {
    return {
      tableName: 'user_job_vacancies',
    };
  }
}

module.exports = UserJobVacancy;
