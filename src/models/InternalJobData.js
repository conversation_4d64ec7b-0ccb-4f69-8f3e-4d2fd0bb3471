'use strict';
const { DataTypes } = require('sequelize');
const AppModel = require('./AppModel');

class InternalJobData extends AppModel {
  /**
   * Associations with other models
   */
  static associate(_models) {
    // No associations needed for this model currently
  }

  /**
   * Model schema
   */
  static schema() {
    return {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      job_division: {
        type: DataTypes.STRING,
        allowNull: true,
        field: 'job_division',
        validate: {
          len: {
            args: [0, 255],
            msg: 'Job division must be less than 255 characters',
          },
        },
      },
      job_group: {
        type: DataTypes.STRING,
        allowNull: true,
        field: 'job_group',
        validate: {
          len: {
            args: [0, 255],
            msg: 'Job group must be less than 255 characters',
          },
        },
      },
      position_name: {
        type: DataTypes.STRING,
        allowNull: true,
        field: 'position_name',
        validate: {
          len: {
            args: [0, 255],
            msg: 'Position name must be less than 255 characters',
          },
        },
      },
      job_classification: {
        type: DataTypes.STRING,
        allowNull: true,
        field: 'job_classification',
        validate: {
          len: {
            args: [0, 255],
            msg: 'Job classification must be less than 255 characters',
          },
        },
      },
      job_family: {
        type: DataTypes.STRING,
        allowNull: true,
        field: 'job_family',
        validate: {
          len: {
            args: [0, 255],
            msg: 'Job family must be less than 255 characters',
          },
        },
      },
      sub_job_family: {
        type: DataTypes.STRING,
        allowNull: true,
        field: 'sub_job_family',
        validate: {
          len: {
            args: [0, 255],
            msg: 'Sub job family must be less than 255 characters',
          },
        },
      },
      main_responsibilities: {
        type: DataTypes.TEXT,
        allowNull: true,
        field: 'main_responsibilities',
      },
      work_input: {
        type: DataTypes.TEXT,
        allowNull: true,
        field: 'work_input',
      },
      work_output: {
        type: DataTypes.STRING,
        allowNull: true,
        field: 'work_output',
        validate: {
          len: {
            args: [0, 255],
            msg: 'Work output must be less than 255 characters',
          },
        },
      },
      success_criteria: {
        type: DataTypes.TEXT,
        allowNull: true,
        field: 'success_criteria',
      },
      requirement: {
        type: DataTypes.TEXT,
        allowNull: true,
        field: 'requirement',
      },
      competency: {
        type: DataTypes.TEXT,
        allowNull: true,
        field: 'competency',
      },
    };
  }

  /**
   * Lifecycle hooks
   */
  static hooks() {
    return {
      beforeCreate: internalJobData => {
        internalJobData.created_at = new Date();
        internalJobData.updated_at = new Date();
      },
      beforeUpdate: internalJobData => {
        internalJobData.updated_at = new Date();
      },
    };
  }

  /**
   * Additional model options
   */
  static options() {
    return {
      indexes: [
        {
          fields: ['position_name'],
        },
        {
          fields: ['job_division', 'job_group'],
        },
      ],
    };
  }
}

module.exports = InternalJobData;
