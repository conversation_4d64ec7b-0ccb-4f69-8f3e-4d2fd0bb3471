const { DataTypes } = require('sequelize');
const AppModel = require('./AppModel');

class UserPosition extends AppModel {
  static schema() {
    return {
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
      },
      role_name: { type: DataTypes.STRING, allowNull: false },
      department: { type: DataTypes.STRING },
      job_grade: { type: DataTypes.STRING },
      starts_at: { type: DataTypes.DATEONLY },
      ends_at: { type: DataTypes.DATEONLY },
    };
  }

  static associate(models) {
    this.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user',
    });
  }
}

module.exports = UserPosition;
