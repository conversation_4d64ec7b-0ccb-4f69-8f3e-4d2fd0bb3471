const ApiController = require('./ApiController');
const UserJobVacanciesService = require('../services/UserJobVacanciesService');
const UserJobVacanciesIndexInput = require('../inputs/UserJobVacanciesIndexInput');
const UserJobVacancyOutput = require('../outputs/UserJobVacancyOutput');

class UserJobVacanciesController extends ApiController {
  constructor() {
    super();
    this.service = new UserJobVacanciesService();
  }

  /**
   * GET /api/v1/user_job_vacancies
   * Lists user recommendations for job vacancies, filterable by job_vacancy_id
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  index = this.createMethod(async (req, res) => {
    const input = new UserJobVacanciesIndexInput(req.query);
    input.validate();

    const result = await this.service.findAll(input.output());
    const output = new UserJobVacancyOutput(result.userJobVacancies, {
      pagination: result.pagination,
    });

    output.renderJsonArray(res);
  });

  /**
   * GET /api/v1/user_job_vacancies/:id
   * Get a specific user job vacancy recommendation by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  show = this.createMethod(async (req, res) => {
    const { id } = req.params;
    const userJobVacancy = await this.service.findById(id);

    const output = new UserJobVacancyOutput(userJobVacancy);
    output.renderJson(res);
  });
}

module.exports = new UserJobVacanciesController();
