# Node.js
node_modules/
npm-debug.log*

# Environment files
.env
.env.*

# macOS
.DS_Store

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by Istanbul
coverage/

# nyc test coverage
.nyc_output/

# Jest test results
test-results/

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components/

# build results
build/
dist/

# misc
.vscode/
.idea/
.history/

# database
src/config/database.json
