version: 2.1

orbs:
  aws-ecr: circleci/aws-ecr@9.3.2
  aws-cli: circleci/aws-cli@5.1.0
  aws-eks: circleci/aws-eks@2.2.0

executors:
  k8s:
    working_directory: ~/paragon-api
    docker:
      - image: cimg/node:20.11
    resource_class: large
    environment:
      NODE_ENV: staging

commands:
  migrate-db:
    steps:
      - run:
          name: Prepare environment files
          command: |
            echo "Creating .env file from cluster configmap..."
            touch .env
            kubectl get cm paragon-app-config -o jsonpath='{.data.\.env}' > .env
            chmod 644 .env

            echo "Creating database.json from cluster configmap..."
            touch database.json
            kubectl get cm paragon-app-config -o jsonpath='{.data.database\.json}' > database.json
            chmod 644 database.json
      - run:
          name: Migrate database
          command: |
            echo "Setting up database migration container..."
            CONTAINER_ID=$(docker create \
              -e NODE_ENV=staging \
              $AWS_ECR_REGISTRY_ID.dkr.ecr.$AWS_REGION.amazonaws.com/paragon-api:$IMAGE_TAG \
              npm run db:migrate)

            echo "Copying environment files to container..."
            docker cp "$(pwd)/.env" "$CONTAINER_ID:/app/.env"
            docker cp "$(pwd)/database.json" "$CONTAINER_ID:/app/src/config/database.json"

            echo "Running database migrations..."
            docker start -a "$CONTAINER_ID"

            docker rm "$CONTAINER_ID"            
            echo "Database migration completed successfully."

jobs:
  deploy-staging:
    executor: k8s
    steps:
      - run:
          name: Start timer
          command: |
            echo "export START_TIME=$(date +%s)" >> $BASH_ENV
      - checkout
      - run:
          name: Set tag image
          command: |
            COMMIT_HASH=$(git rev-parse --short HEAD)
            echo "Using commit hash: $COMMIT_HASH"
            echo "export IMAGE_TAG=$COMMIT_HASH" >> $BASH_ENV
      - setup_remote_docker:
          docker_layer_caching: false
      - aws-ecr/build_and_push_image:
          repo: paragon-api
          tag: $IMAGE_TAG
          auth:
            - aws-cli/setup:
                region: $AWS_REGION
          region: $AWS_REGION
          checkout: false
          account_id: $AWS_ECR_REGISTRY_ID
      - aws-eks/update-kubeconfig-with-authenticator:
          cluster-name: $CLUSTER_NAME
          install-kubectl: true
          kubectl-version: v1.27.7
          aws-region: $AWS_REGION
      - migrate-db
      - run:
          name: Set deployment image of paragon-app
          command: |
            echo "Updating paragon-app deployment with image tag: $IMAGE_TAG"
            kubectl set image deploy/paragon-app paragon-app=$AWS_ECR_REGISTRY_ID.dkr.ecr.$AWS_REGION.amazonaws.com/paragon-api:$IMAGE_TAG
      - run:
          name: Annotate deployment with commit hash
          command: |
            echo "Annotating paragon-app deployment with commit hash: $IMAGE_TAG"
            kubectl annotate deploy/paragon-app kubernetes.io/change-cause="$CIRCLE_BRANCH:$CIRCLE_SHA1:$IMAGE_TAG"
      - run:
          name: End timer and calculate duration
          command: |
            echo "export END_TIME=$(date +%s)" >> $BASH_ENV
            echo 'export DURATION=$(printf "%02d:%02d" $(( ($END_TIME - $START_TIME) / 60 )) $(( ($END_TIME - $START_TIME) % 60 )))' >> $BASH_ENV
workflows:
  version: 2
  deploy-k8s-cluster:
    jobs:
      - approval:
          type: approval
      - deploy-staging:
          name: deploy staging to k8s cluster
          requires: [approval]
          filters:
            branches: { ignore: [master, /(release|hotfix|v)\/.*/] }
