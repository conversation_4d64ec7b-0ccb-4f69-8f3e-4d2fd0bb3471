'use strict';

const BaseFactory = require('./BaseFactory');
const { UserPosition } = require('../../src/models');

class UserPositionFactory extends BaseFactory {
  constructor() {
    super(UserPosition);
  }

  static defaultAttributes() {
    const startDate = this.faker.date.past({ years: 3 });
    const endDate = this.faker.datatype.boolean()
      ? this.faker.date.between({ from: startDate, to: new Date() })
      : null;

    return {
      role_name: this.faker.person.jobTitle(),
      department: this.faker.person.jobArea(),
      job_grade: this.faker.helpers.arrayElement(['L1', 'L2', 'L3', 'L4', 'L5', 'L6', 'L7']),
      starts_at: startDate,
      ends_at: endDate,
    };
  }
}

module.exports = UserPositionFactory;
